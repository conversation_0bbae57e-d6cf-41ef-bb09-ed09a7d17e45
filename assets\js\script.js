
        $(document).ready(function () {
  // Testimonial Carousel - 3 cards with autoplay
  $("#slider-1").owlCarousel({
    items: 3,
    loop: true,
    autoplay: true,
    autoplayTimeout: 1000,
    autoplayHoverPause: false,
    nav: false,
    dots: true,

    navText: [
      '<i class="fas fa-chevron-left"></i>',
      '<i class="fas fa-chevron-right"></i>'
    ],
    responsive: {
      0: {
        items: 1,
        nav: true
      },
      768: {
        items: 2,
        nav: false
      },
      992: {
        items: 3,
        nav: false
      }
    },
    rtl: false, // Right to left is false for normal left to right flow
    margin: 30
  });

  // Simple Navbar Color Change - BLACK at top, BLUE when scrolling
  function updateNavbar() {
    var scrollTop = $(window).scrollTop();
    if (scrollTop <= 10) {
      $('header.head').removeClass('navbar-blue').addClass('navbar-black');
    } else {
      $('header.head').removeClass('navbar-black').addClass('navbar-blue');
    }
  }

  updateNavbar(); // Check on page load

  $(window).scroll(function () {
    updateNavbar();
  });

  // FAQ highlight script
  $(".faq-item").click(function () {
    $(".faq-item").removeClass("active");
    $(this).addClass("active");
  });

  // Mobile Menu Functions
  function openMobileMenu() {
    $('header.head').addClass('menu-open');
    $('body').css('overflow', 'hidden');
  }

  function closeMobileMenu() {
    $('header.head').removeClass('menu-open');
    $('body').css('overflow', '');
  }

  $('.menu-toggle').click(function (e) {
    e.preventDefault();
    openMobileMenu();
  });

  $('.mobile-close-btn').click(function (e) {
    e.preventDefault();
    closeMobileMenu();
  });

  $('.mobile-menu-overlay').click(function () {
    closeMobileMenu();
  });

  $(document).keydown(function (e) {
    if (e.key === 'Escape' && $('header.head').hasClass('menu-open')) {
      closeMobileMenu();
    }
  });

  $('.mobile-nav-link:not(.mobile-dropdown-toggle)').click(function () {
    closeMobileMenu();
  });

  $('.mobile-dropdown-toggle').click(function (e) {
    e.preventDefault();
    var $dropdownItem = $(this).closest('.mobile-dropdown');
    var $dropdownMenu = $dropdownItem.find('.mobile-dropdown-menu');

    $dropdownItem.toggleClass('active');
    if ($dropdownItem.hasClass('active')) {
      $dropdownMenu.show();
    } else {
      $dropdownMenu.hide();
    }
  });

  $('.mobile-dropdown-item').click(function () {
    closeMobileMenu();
  });
}); // ✅ Properly closed

// accordion
$(document).ready(function () {
  $("#accordionFlushExample .accordion-button").on("click", function () {
    var targetId = $(this).data("bs-target");

    // close all other answers
    $(".accordion-collapse").each(function () {
      if ($(this).attr("id") !== targetId.substring(1)) {
        $(this).removeClass("show");
      }
    });

    // toggle clicked one
    $(targetId).toggleClass("show");
  });
});
